from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from user.permissions import IsAdmin
from .services import QuotaValidationService, SubscriptionDataService
from devproject.utils.utils import LoggingMixin


class QuotaStatusView(LoggingMixin, APIView):
    """Get current quota status"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Get current subscription quota status
        
        Returns:
            - Organization information
            - Current and maximum user limits
            - Feature availability
            - Subscription status and expiry
        """
        try:
            result = QuotaValidationService.get_quota_status()
            
            if 'error' in result:
                return Response({
                    "message": "Subscription information not found",
                    "error": result['error']
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "message": "Quota status retrieved successfully",
                "data": result
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            self.log_error(f"Error retrieving quota status: {str(e)}")
            return Response({
                "message": "Failed to retrieve quota status",
                "error": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserCreationValidationView(LoggingMixin, APIView):
    """Check if new user can be created"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Validate if a new user can be created based on current quota
        
        Returns:
            - allowed: boolean indicating if user creation is allowed
            - quota_info: current quota information
            - error: error message if creation not allowed
        """
        try:
            result = QuotaValidationService.can_create_user()
            
            if result['allowed']:
                return Response({
                    "message": "User creation allowed",
                    "data": result
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "message": "User creation not allowed",
                    "data": result
                }, status=status.HTTP_403_FORBIDDEN)
                
        except Exception as e:
            self.log_error(f"Error validating user creation: {str(e)}")
            return Response({
                "message": "Failed to validate user creation",
                "error": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FeatureAccessView(LoggingMixin, APIView):
    """Check feature access"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Check if a specific feature is enabled in current subscription
        
        Query Parameters:
            feature: Name of the feature to check
            
        Returns:
            - feature: The feature name that was checked
            - has_access: boolean indicating if feature is available
        """
        feature_name = request.query_params.get('feature')
        if not feature_name:
            return Response({
                "message": "Validation failed",
                "error": "feature parameter is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            has_access = QuotaValidationService.check_feature_access(feature_name)
            
            return Response({
                "message": "Feature access checked successfully",
                "data": {
                    'feature': feature_name,
                    'has_access': has_access
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            self.log_error(f"Error checking feature access for {feature_name}: {str(e)}")
            return Response({
                "message": "Failed to check feature access",
                "error": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SubscriptionInfoView(LoggingMixin, APIView):
    """Get current subscription information"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Get current subscription information
        
        Returns different levels of detail based on user permissions:
        - Admin users: Full subscription data
        - Regular users: Limited subscription data (no sensitive info)
        """
        try:
            data = SubscriptionDataService.get_subscription_data()
            
            if not data:
                return Response({
                    "message": "Subscription not found",
                    "error": "No subscription found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Don't expose sensitive information to non-admin users
            if not request.user.is_superuser:
                # Remove sensitive fields for non-admin users
                safe_data = {
                    'organization_name': data.get('organization_name'),
                    'tier_name': data.get('tier_name'),
                    'status': data.get('status'),
                    'expires_at': data.get('expires_at'),
                    'quota': data.get('quota'),
                    'features': data.get('features')
                }
                return Response({
                    "message": "Subscription information retrieved successfully",
                    "data": safe_data
                }, status=status.HTTP_200_OK)
            
            return Response({
                "message": "Subscription information retrieved successfully",
                "data": data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            self.log_error(f"Error retrieving subscription info: {str(e)}")
            return Response({
                "message": "Failed to retrieve subscription information",
                "error": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
