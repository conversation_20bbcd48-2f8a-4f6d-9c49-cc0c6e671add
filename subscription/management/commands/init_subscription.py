import json
from django.core.management.base import BaseCommand
from django.utils import timezone
from setting.models import SystemSettings
from user.models import User


class Command(BaseCommand):
    help = 'Initialize subscription data in SystemSettings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization-name',
            type=str,
            default='Viriyah',
            help='Organization name for the subscription'
        )
        parser.add_argument(
            '--tier',
            type=str,
            choices=['premium', 'enterprise', 'enterprise_plus'],
            default='enterprise_plus',
            help='Subscription tier'
        )
        parser.add_argument(
            '--max-users',
            type=int,
            default=50,
            help='Maximum active users allowed'
        )

    def handle(self, *args, **options):
        organization_name = options['organization_name']
        tier = options['tier']
        max_users = options['max_users']
        
        # Map tier to display names
        tier_names = {
            'premium': 'Premium',
            'enterprise': 'Enterprise',
            'enterprise_plus': 'Enterprise Plus'
        }
        
        # Create subscription data
        subscription_data = {
            "organization_name": organization_name,
            "subscription_key": "ENTP-C3D4E5F6G7H8I9J0",
            "tier_id": tier,
            "tier_name": tier_names[tier],
            "status": "active",
            "activated_on": timezone.now().isoformat(),
            "expires_at": "2025-12-31T23:59:59Z",
            "quota": {
                "max_active_users": max_users,
                "max_line_accounts": "unlimited",
                "max_ai_workflow_units": "unlimited",
                "max_messages_per_min": 200,
                "max_storage_gb": 2000,
            },
            "features": {
                "custom_transfer_algo": True,
                "custom_case_desc": True,
                "custom_ai_workflow": True,
                "ai_quick_reply": True,
                "ai_smart_reply": True,
                "ai_memory": True,
                "crm_integration": True,
                "crm_notify_claim": True,
                "crm_case_system": True,
                "dashboard_sla_config": True,
                "dashboard_sla_alert": True,
                "broadcasting": True,
            },
            "metadata": {
                "billing_contact": "<EMAIL>",
                "technical_contact": "<EMAIL>",
            }
        }
        
        # Get system user for created_by field
        try:
            system_user = User.objects.filter(is_superuser=True).first()
        except User.DoesNotExist:
            system_user = None
        
        # Create or update SystemSettings entry
        setting, created = SystemSettings.objects.get_or_create(
            key='SUBSCRIPTION_DATA',
            defaults={
                'value_type': 'json',
                'description': 'Organization subscription data',
                'updated_by': system_user
            }
        )
        
        setting.set_json_value(subscription_data)
        if system_user:
            setting.updated_by = system_user
        setting.save()
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created subscription data for {organization_name} '
                    f'with {tier_names[tier]} tier ({max_users} max users)'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated subscription data for {organization_name} '
                    f'with {tier_names[tier]} tier ({max_users} max users)'
                )
            )
        
        # Display current active user count
        current_users = User.objects.filter(is_active=True).count()
        self.stdout.write(
            self.style.WARNING(
                f'Current active users: {current_users}/{max_users}'
            )
        )
