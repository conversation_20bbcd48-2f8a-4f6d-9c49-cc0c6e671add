import json
from django.core.management.base import BaseCommand
from django.utils import timezone
from setting.models import SystemSettings
from user.models import User


class Command(BaseCommand):
    help = 'Initialize subscription data in SystemSettings with predefined tier configurations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tier',
            type=str,
            choices=['premium', 'enterprise', 'enterprise_plus'],
            required=True,
            help='Subscription tier (premium, enterprise, enterprise_plus)'
        )
        parser.add_argument(
            '--company',
            type=str,
            default='',
            help='Company/Organization name'
        )

    def handle(self, *args, **options):
        tier = options['tier']
        company_name = options['company']

        # Define tier configurations
        tier_configs = {
            'premium': {
                "organization_name": company_name,
                "subscription_key": "PREM-A1B2C3D4E5F6G7H8",
                "tier_id": "premium",
                "tier_name": "Premium",
                "status": "active",
                "activated_on": "2025-08-29T22:15:00Z",
                "expires_at": "2025-12-31T23:59:59Z",
                "quota": {
                    "max_active_users": 5,
                    "max_line_accounts": 5,
                    "max_ai_workflow_units": 1,
                    "max_messages_per_min": 30,
                    "max_storage_gb": 250
                },
                "features": {
                    "custom_transfer_algo": False,
                    "custom_case_desc": False,
                    "custom_ai_workflow": False,
                    "ai_quick_reply": True,
                    "ai_smart_reply": False,
                    "ai_memory": False,
                    "crm_integration": False,
                    "crm_notify_claim": False,
                    "crm_case_system": False,
                    "dashboard_sla_config": False,
                    "dashboard_sla_alert": False,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            },
            'enterprise': {
                "organization_name": company_name,
                "subscription_key": "ENT-B2C3D4E5F6G7H8I9",
                "tier_id": "enterprise",
                "tier_name": "Enterprise",
                "status": "active",
                "activated_on": "2025-08-29T22:15:00Z",
                "expires_at": "2025-12-31T23:59:59Z",
                "quota": {
                    "max_active_users": 20,
                    "max_line_accounts": 10,
                    "max_ai_workflow_units": 5,
                    "max_messages_per_min": 100,
                    "max_storage_gb": 1000
                },
                "features": {
                    "custom_transfer_algo": True,
                    "custom_case_desc": True,
                    "custom_ai_workflow": True,
                    "ai_quick_reply": True,
                    "ai_smart_reply": True,
                    "ai_memory": True,
                    "crm_integration": True,
                    "crm_notify_claim": False,
                    "crm_case_system": False,
                    "dashboard_sla_config": True,
                    "dashboard_sla_alert": True,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            },
            'enterprise_plus': {
                "organization_name": company_name,
                "subscription_key": "ENTP-C3D4E5F6G7H8I9J0",
                "tier_id": "enterprise_plus",
                "tier_name": "Enterprise Plus",
                "status": "active",
                "activated_on": "2025-08-29T22:15:00Z",
                "expires_at": "2025-12-31T23:59:59Z",
                "quota": {
                    "max_active_users": 50,
                    "max_line_accounts": "unlimited",
                    "max_ai_workflow_units": "unlimited",
                    "max_messages_per_min": 200,
                    "max_storage_gb": 2000
                },
                "features": {
                    "custom_transfer_algo": True,
                    "custom_case_desc": True,
                    "custom_ai_workflow": True,
                    "ai_quick_reply": True,
                    "ai_smart_reply": True,
                    "ai_memory": True,
                    "crm_integration": True,
                    "crm_notify_claim": True,
                    "crm_case_system": True,
                    "dashboard_sla_config": True,
                    "dashboard_sla_alert": True,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            }
        }

        # Get the subscription data for the selected tier
        subscription_data = tier_configs[tier]

        # Get system user for created_by field
        try:
            system_user = User.objects.filter(is_superuser=True).first()
        except User.DoesNotExist:
            system_user = None

        # Create or update SystemSettings entry
        setting, created = SystemSettings.objects.get_or_create(
            key='SUBSCRIPTION_DATA',
            defaults={
                'value_type': 'json',
                'description': 'Organization subscription data',
                'updated_by': system_user
            }
        )

        setting.set_json_value(subscription_data)
        if system_user:
            setting.updated_by = system_user
        setting.save()

        # Get tier info for display
        tier_name = subscription_data['tier_name']
        max_users = subscription_data['quota']['max_active_users']

        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created subscription data for {company_name} '
                    f'with {tier_name} tier ({max_users} max users)'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated subscription data for {company_name} '
                    f'with {tier_name} tier ({max_users} max users)'
                )
            )

        # Display current active user count
        current_users = User.objects.filter(is_active=True).count()

        # Handle unlimited users display
        max_users_display = max_users if max_users != "unlimited" else "unlimited"
        self.stdout.write(
            self.style.WARNING(
                f'Current active users: {current_users}/{max_users_display}'
            )
        )

        # Show tier-specific features
        self.stdout.write('\n' + self.style.SUCCESS('Tier Features:'))
        features = subscription_data['features']
        enabled_features = [feature for feature, enabled in features.items() if enabled]
        disabled_features = [feature for feature, enabled in features.items() if not enabled]

        if enabled_features:
            self.stdout.write(self.style.SUCCESS(f'  ✅ Enabled: {", ".join(enabled_features)}'))
        if disabled_features:
            self.stdout.write(f'  ❌ Disabled: {", ".join(disabled_features)}')

        # Show quota limits
        self.stdout.write('\n' + self.style.SUCCESS('Quota Limits:'))
        quota = subscription_data['quota']
        for key, value in quota.items():
            self.stdout.write(f'  📊 {key}: {value}')
